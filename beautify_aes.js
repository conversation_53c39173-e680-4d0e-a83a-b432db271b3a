const fs = require('fs');

// 读取提取的AES代码
const aesCode = fs.readFileSync('aes_decrypter_extracted.js', 'utf8');

console.log('=== AES解密器代码分析 ===\n');

// 基本美化：添加换行和缩进
function beautifyCode(code) {
    return code
        .replace(/;/g, ';\n')
        .replace(/\{/g, '{\n  ')
        .replace(/\}/g, '\n}')
        .replace(/,/g, ',\n  ')
        .replace(/function/g, '\nfunction')
        .replace(/var /g, '\nvar ')
        .replace(/return /g, '\nreturn ')
        .replace(/for\(/g, '\nfor(')
        .replace(/if\(/g, '\nif(')
        .replace(/\n\s*\n/g, '\n'); // 移除多余空行
}

console.log('=== 美化后的AES解密器代码 ===\n');
const beautifiedCode = beautifyCode(aesCode);

// 保存美化后的代码
fs.writeFileSync('aes_decrypter_beautified.js', beautifiedCode);

console.log('美化后的代码已保存到 aes_decrypter_beautified.js\n');

console.log('=== 关键组件分析 ===\n');

// 分析关键组件
console.log('1. AES解密器类 (il):');
console.log('   - 这是主要的AES解密实现');
console.log('   - 支持AES-128, AES-192, AES-256 (key长度: 4, 6, 8)');
console.log('   - 实现了标准的AES解密算法');

console.log('\n2. 异步流处理器 (nl):');
console.log('   - 用于异步处理解密任务');
console.log('   - 避免阻塞主线程');

console.log('\n3. 解密处理器 (rl):');
console.log('   - 处理大块数据的解密');
console.log('   - 使用CBC模式进行解密');

console.log('\n=== 核心解密函数分析 ===\n');

// 查找核心解密函数
const decryptFunctionMatch = aesCode.match(/e\.prototype\.decrypt=function\([^}]+\}/);
if (decryptFunctionMatch) {
    console.log('找到核心解密函数:');
    console.log(beautifyCode(decryptFunctionMatch[0]));
}

console.log('\n=== M3U8解密相关分析 ===\n');

// 从原始文件中查找更多M3U8相关的解密代码
const originalContent = fs.readFileSync('video.min.7.9.7.js', 'utf8');

// 查找可能的M3U8解密函数
const m3u8DecryptRegex = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:m3u8|M3U8|segment|decrypt)[^}]{0,300}\}/gi;
const m3u8Functions = originalContent.match(m3u8DecryptRegex);

if (m3u8Functions) {
    console.log('找到可能的M3U8相关函数:');
    m3u8Functions.slice(0, 3).forEach((func, index) => {
        console.log(`\n--- M3U8函数 ${index + 1} ---`);
        console.log(beautifyCode(func));
    });
}

console.log('\n=== 使用建议 ===\n');
console.log('要解密M3U8视频流，你需要:');
console.log('1. 获取加密密钥 (通常从.key文件或EXT-X-KEY标签)');
console.log('2. 获取初始化向量 (IV)');
console.log('3. 使用AES-128-CBC模式解密视频片段');
console.log('4. 处理PKCS7填充');

console.log('\n=== 关键参数说明 ===\n');
console.log('- key: AES密钥 (128位/16字节)');
console.log('- iv: 初始化向量 (128位/16字节)');
console.log('- encrypted data: 加密的视频片段数据');
console.log('- 解密模式: AES-128-CBC');

console.log('\n分析完成！');
