function Qu(e,t,i){var n,r,s,a,o,u,l,c,h,d=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),p=new il(Array.prototype.slice.call(t)),f=new Uint8Array(e.byteLength),m=new Int32Array(f.buffer);for(n=i[0],r=i[1],s=i[2],a=i[3],h=0;h<d.length;h+=4)o=$u(d[h]),u=$u(d[h+1]),l=$u(d[h+2]),c=$u(d[h+3]),p.decrypt(o,u,l,c,m,h),m[h]=$u(m[h]^n),m[h+1]=$u(m[h+1]^r),m[h+2]=$u(m[h+2]^s),m[h+3]=$u(m[h+3]^a),n=o,r=u,s=l,a=c;return f}