# Video.js AES解密器分析报告

## 概述
通过分析 `video.min.7.9.7.js` 文件，我发现了完整的AES解密实现，主要用于解密HLS (HTTP Live Streaming) 中的加密视频片段。

## 核心组件分析

### 1. AES解密器类 (`il`)
这是主要的AES解密实现，特点：
- **支持的密钥长度**: AES-128, AES-192, AES-256 (对应key长度: 4, 6, 8个32位字)
- **算法实现**: 标准AES解密算法
- **查找表优化**: 使用预计算的查找表提高性能

#### 核心解密方法
```javascript
e.prototype.decrypt = function(e, t, i, n, r, s) {
    // e, t, i, n: 输入的4个32位字 (128位数据块)
    // r: 输出缓冲区
    // s: 输出偏移量
    
    var c = this._key[1];  // 解密密钥
    var h = e ^ c[0];      // 初始轮密钥加
    var d = n ^ c[1];
    var p = i ^ c[2]; 
    var f = t ^ c[3];
    
    // 执行多轮解密变换
    // 使用查找表进行字节替换和列混合
    // ...
}
```

### 2. 异步流处理器 (`nl`)
- **作用**: 避免长时间解密操作阻塞主线程
- **实现**: 使用setTimeout进行任务调度
- **队列管理**: 维护解密任务队列

### 3. 解密处理器 (`rl`)
- **功能**: 处理大块数据的分段解密
- **模式**: AES-128-CBC (密码块链接模式)
- **分块处理**: 将大数据分成小块异步处理

## M3U8解密流程

### 1. 加密信息获取
从M3U8播放列表中的 `EXT-X-KEY` 标签获取：
```
#EXT-X-KEY:METHOD=AES-128,URI="https://example.com/key.key",IV=0x12345678901234567890123456789012
```

### 2. 关键参数
- **METHOD**: 加密方法 (通常是AES-128)
- **URI**: 密钥文件地址
- **IV**: 初始化向量 (128位)

### 3. 解密过程
```javascript
// 伪代码示例
function decryptSegment(encryptedData, key, iv) {
    // 1. 创建AES解密器
    var aesDecrypter = new il(key);
    
    // 2. 使用CBC模式解密
    var decryptedData = aesDecrypter.decrypt(encryptedData, iv);
    
    // 3. 移除PKCS7填充
    return removePKCS7Padding(decryptedData);
}
```

## 技术细节

### AES-128-CBC模式
- **块大小**: 128位 (16字节)
- **密钥长度**: 128位 (16字节)
- **IV长度**: 128位 (16字节)
- **填充方式**: PKCS7

### 字节序处理
代码中使用了字节序转换函数：
```javascript
function $u(e) {
    // 大端序到小端序转换
    return (e << 24) | ((e & 0xff00) << 8) | ((e & 0xff0000) >> 8) | (e >>> 24);
}
```

## 实际应用

### 解密M3U8视频流的步骤：

1. **解析M3U8文件**
   - 提取 `EXT-X-KEY` 信息
   - 获取视频片段URL列表

2. **获取解密密钥**
   - 从KEY URI下载密钥文件
   - 密钥通常是16字节的二进制数据

3. **解密视频片段**
   ```javascript
   // 对每个加密的.ts片段
   for (let segment of encryptedSegments) {
       let iv = segment.iv || defaultIV;
       let decryptedData = decryptSegment(segment.data, key, iv);
       // 处理解密后的数据
   }
   ```

4. **处理解密结果**
   - 移除PKCS7填充
   - 合并解密后的片段
   - 输出完整视频

## 关键发现

### 1. 完整的AES实现
- 包含完整的AES-128解密算法
- 支持CBC模式
- 包含PKCS7填充处理

### 2. 优化特性
- 使用查找表加速计算
- 异步处理避免阻塞
- 分块处理大文件

### 3. HLS兼容性
- 符合HLS标准的AES-128加密
- 支持标准的IV处理
- 兼容常见的M3U8格式

## 使用建议

### 对于M3U8解密项目：

1. **提取核心代码**
   - 可以直接使用 `il` 类进行AES解密
   - 需要实现CBC模式的IV链接
   - 添加PKCS7填充移除

2. **性能优化**
   - 使用异步处理避免界面卡顿
   - 考虑Web Worker进行后台解密
   - 缓存解密结果

3. **错误处理**
   - 验证密钥长度和格式
   - 处理网络请求失败
   - 验证解密结果完整性

## 总结

这个Video.js中的AES解密器是一个完整、高效的实现，完全可以用于M3U8视频流的解密。它包含了所有必要的组件：AES算法实现、CBC模式支持、异步处理机制等。

对于你的M3U8解密项目，可以直接使用这些代码作为基础，只需要添加M3U8解析和密钥管理功能即可。
