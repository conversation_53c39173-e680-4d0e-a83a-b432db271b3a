const fs = require('fs');

const content = fs.readFileSync('video.min.7.9.7.js', 'utf8');

console.log('=== 提取完整的解密函数实现 ===\n');

// 1. 提取Qu函数 - 这看起来是主要的解密函数
console.log('1. 提取Qu函数 (主要解密函数):');
const quFunctionMatch = content.match(/function Qu\([^}]+\{[^}]+\}/g);
if (quFunctionMatch) {
    console.log('找到Qu函数:');
    quFunctionMatch.forEach((func, index) => {
        console.log(`\n--- Qu函数 ${index + 1} ---`);
        // 尝试找到完整的函数体
        const startIndex = content.indexOf(func);
        let braceCount = 0;
        let endIndex = startIndex;
        let inFunction = false;
        
        for (let i = startIndex; i < content.length; i++) {
            if (content[i] === '{') {
                braceCount++;
                inFunction = true;
            } else if (content[i] === '}') {
                braceCount--;
                if (inFunction && braceCount === 0) {
                    endIndex = i + 1;
                    break;
                }
            }
        }
        
        const fullFunction = content.substring(startIndex, endIndex);
        console.log(fullFunction);
        
        // 保存到文件
        fs.writeFileSync(`qu_function_${index + 1}.js`, fullFunction);
    });
}

// 2. 提取decryptChunk_函数
console.log('\n2. 提取decryptChunk_函数:');
const decryptChunkMatch = content.match(/decryptChunk_=function[^}]+\{[^}]+\}/g);
if (decryptChunkMatch) {
    console.log('找到decryptChunk_函数:');
    decryptChunkMatch.forEach((func, index) => {
        console.log(`\n--- decryptChunk_ ${index + 1} ---`);
        
        // 找到完整函数
        const startIndex = content.indexOf(func);
        let braceCount = 0;
        let endIndex = startIndex;
        let inFunction = false;
        
        for (let i = startIndex; i < content.length; i++) {
            if (content[i] === '{') {
                braceCount++;
                inFunction = true;
            } else if (content[i] === '}') {
                braceCount--;
                if (inFunction && braceCount === 0) {
                    endIndex = i + 1;
                    break;
                }
            }
        }
        
        const fullFunction = content.substring(startIndex, endIndex);
        console.log(fullFunction);
        
        fs.writeFileSync(`decrypt_chunk_${index + 1}.js`, fullFunction);
    });
}

// 3. 查找HLS相关的解密处理
console.log('\n3. 查找HLS解密处理:');
const hlsDecryptRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[=:]\s*function[^}]*(?:hls|HLS|segment|key|iv|decrypt)[^}]{50,}\}/gi;
const hlsDecryptFunctions = content.match(hlsDecryptRegex);
if (hlsDecryptFunctions) {
    console.log(`找到 ${hlsDecryptFunctions.length} 个HLS解密相关函数:`);
    hlsDecryptFunctions.slice(0, 5).forEach((func, index) => {
        console.log(`\n--- HLS解密函数 ${index + 1} ---`);
        console.log(func.substring(0, 300) + '...');
    });
}

// 4. 查找EXT-X-KEY处理
console.log('\n4. 查找EXT-X-KEY处理:');
const extXKeyIndex = content.indexOf('EXT-X-KEY');
if (extXKeyIndex !== -1) {
    console.log('找到EXT-X-KEY处理代码:');
    const start = Math.max(0, extXKeyIndex - 200);
    const end = Math.min(content.length, extXKeyIndex + 500);
    const extXKeyCode = content.substring(start, end);
    console.log(extXKeyCode);
    
    fs.writeFileSync('ext_x_key_handler.js', extXKeyCode);
}

// 5. 查找AES-128处理
console.log('\n5. 查找AES-128处理:');
const aes128Index = content.indexOf('AES-128');
if (aes128Index !== -1) {
    console.log('找到AES-128处理代码:');
    const start = Math.max(0, aes128Index - 300);
    const end = Math.min(content.length, aes128Index + 800);
    const aes128Code = content.substring(start, end);
    console.log(aes128Code);
    
    fs.writeFileSync('aes_128_handler.js', aes128Code);
}

console.log('\n=== 提取完成 ===');
