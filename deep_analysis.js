const fs = require('fs');

// 读取完整的video.js文件
const content = fs.readFileSync('video.min.7.9.7.js', 'utf8');

console.log('=== 深度分析M3U8解密实现 ===\n');

// 1. 查找所有与HLS/M3U8相关的代码
console.log('1. 搜索HLS/M3U8相关关键词:');
const hlsKeywords = ['hls', 'HLS', 'm3u8', 'M3U8', 'segment', 'playlist', 'EXT-X-KEY', 'AES-128'];
hlsKeywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'gi');
    const matches = content.match(regex);
    if (matches) {
        console.log(`  "${keyword}": ${matches.length} 次`);
    }
});

// 2. 查找完整的解密函数实现
console.log('\n2. 查找完整的解密函数:');
const decryptFunctions = [
    /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*decrypt[^}]*\}/gi,
    /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]*decrypt[^}]*\}/gi,
    /decrypt\s*[:=]\s*function[^}]*\}/gi
];

decryptFunctions.forEach((regex, index) => {
    const matches = content.match(regex);
    if (matches) {
        console.log(`\n  解密函数模式 ${index + 1}: 找到 ${matches.length} 个`);
        matches.slice(0, 3).forEach((match, i) => {
            console.log(`    函数 ${i + 1}: ${match.substring(0, 100)}...`);
        });
    }
});

// 3. 查找M.prototype相关的所有方法
console.log('\n3. 查找所有M.prototype方法:');
const mPrototypeRegex = /M\.prototype\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*[^;]+/g;
const mMethods = content.match(mPrototypeRegex);
if (mMethods) {
    console.log(`  找到 ${mMethods.length} 个M.prototype方法:`);
    mMethods.forEach((method, index) => {
        console.log(`    ${index + 1}. ${method.substring(0, 80)}...`);
    });
} else {
    console.log('  未找到M.prototype方法');
}

// 4. 查找类似M的其他对象的encrypt/decrypt方法
console.log('\n4. 查找其他对象的encrypt/decrypt方法:');
const otherDecryptRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\.prototype\.(encrypt|decrypt)\s*=\s*[^;]+/g;
const otherMethods = content.match(otherDecryptRegex);
if (otherMethods) {
    console.log(`  找到 ${otherMethods.length} 个其他加密/解密方法:`);
    otherMethods.forEach((method, index) => {
        console.log(`    ${index + 1}. ${method.substring(0, 100)}...`);
    });
}

// 5. 查找包含"key"和"iv"的函数
console.log('\n5. 查找包含key和iv的函数:');
const keyIvRegex = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:key|iv)[^}]*\}/gi;
const keyIvFunctions = content.match(keyIvRegex);
if (keyIvFunctions) {
    console.log(`  找到 ${keyIvFunctions.length} 个包含key/iv的函数`);
    keyIvFunctions.slice(0, 5).forEach((func, index) => {
        console.log(`\n    函数 ${index + 1}:`);
        console.log(`    ${func.substring(0, 200)}...`);
    });
}

// 6. 查找CBC模式相关代码
console.log('\n6. 查找CBC模式相关代码:');
const cbcRegex = /[^a-zA-Z]cbc[^a-zA-Z]|CBC|cipher.*block.*chain/gi;
const cbcMatches = content.match(cbcRegex);
if (cbcMatches) {
    console.log(`  找到 ${cbcMatches.length} 个CBC相关引用`);
}

// 7. 查找完整的解密工作流
console.log('\n7. 查找解密工作流:');
const workflowRegex = /function[^}]*(?:segment|decrypt|key|iv)[^}]*\{[^}]{100,}\}/gi;
const workflows = content.match(workflowRegex);
if (workflows) {
    console.log(`  找到 ${workflows.length} 个可能的解密工作流函数`);
    workflows.slice(0, 3).forEach((workflow, index) => {
        console.log(`\n    工作流 ${index + 1}:`);
        console.log(`    ${workflow.substring(0, 300)}...`);
    });
}

console.log('\n=== 分析完成 ===');
