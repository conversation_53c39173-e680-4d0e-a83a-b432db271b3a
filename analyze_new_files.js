const fs = require('fs');

console.log('=== 分析新下载的文件 ===\n');

// 1. 分析videojs-plugins.min.js
console.log('1. 分析 videojs-plugins.min.js:');
try {
    const pluginsContent = fs.readFileSync('videojs-plugins.min.js', 'utf8');
    console.log(`文件大小: ${pluginsContent.length} 字符`);
    console.log(`文件行数: ${pluginsContent.split('\n').length} 行`);
    
    // 搜索关键词
    const pluginKeywords = ['encrypt', 'decrypt', 'AES', 'key', 'iv', 'cipher', 'm3u8', 'hls', 'segment'];
    console.log('\n关键词搜索结果:');
    pluginKeywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'gi');
        const matches = pluginsContent.match(regex);
        if (matches) {
            console.log(`  "${keyword}": ${matches.length} 次`);
        }
    });
    
    // 查找特殊的解密函数
    const decryptFunctions = pluginsContent.match(/[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*function[^}]*(?:decrypt|encrypt|cipher)[^}]*\}/gi);
    if (decryptFunctions) {
        console.log(`\n找到 ${decryptFunctions.length} 个加密/解密函数:`);
        decryptFunctions.slice(0, 3).forEach((func, index) => {
            console.log(`  函数 ${index + 1}: ${func.substring(0, 100)}...`);
        });
    }
    
} catch (error) {
    console.log(`读取videojs-plugins.min.js失败: ${error.message}`);
}

// 2. 分析chunk-vendors.5b9b0eed.js
console.log('\n2. 分析 chunk-vendors.5b9b0eed.js:');
try {
    const vendorsContent = fs.readFileSync('chunk-vendors.5b9b0eed.js', 'utf8');
    console.log(`文件大小: ${vendorsContent.length} 字符`);
    console.log(`文件行数: ${vendorsContent.split('\n').length} 行`);
    
    // 搜索关键词
    const vendorKeywords = ['encrypt', 'decrypt', 'AES', 'key', 'iv', 'cipher', 'm3u8', 'hls', 'segment', 'crypto'];
    console.log('\n关键词搜索结果:');
    vendorKeywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'gi');
        const matches = vendorsContent.match(regex);
        if (matches) {
            console.log(`  "${keyword}": ${matches.length} 次`);
        }
    });
    
    // 查找crypto相关的实现
    const cryptoRegex = /crypto[a-zA-Z]*|CryptoJS|aes[a-zA-Z]*|AES[a-zA-Z]*/gi;
    const cryptoMatches = vendorsContent.match(cryptoRegex);
    if (cryptoMatches) {
        console.log(`\n找到 ${cryptoMatches.length} 个crypto相关引用`);
        // 去重并显示
        const uniqueCrypto = [...new Set(cryptoMatches)];
        console.log('  唯一的crypto引用:', uniqueCrypto.slice(0, 10));
    }
    
    // 查找可能的M.prototype实现
    const mPrototypeRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\.prototype\.(encrypt|decrypt)\s*=\s*function/gi;
    const mPrototypeMethods = vendorsContent.match(mPrototypeRegex);
    if (mPrototypeMethods) {
        console.log(`\n找到 ${mPrototypeMethods.length} 个prototype加密/解密方法:`);
        mPrototypeMethods.forEach((method, index) => {
            console.log(`  ${index + 1}. ${method}`);
        });
    }
    
    // 查找完整的加密解密函数
    const fullDecryptFunctions = vendorsContent.match(/function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:decrypt|encrypt|cipher)[^}]{50,}\}/gi);
    if (fullDecryptFunctions) {
        console.log(`\n找到 ${fullDecryptFunctions.length} 个完整的加密/解密函数`);
        fullDecryptFunctions.slice(0, 3).forEach((func, index) => {
            console.log(`\n  函数 ${index + 1}:`);
            console.log(`  ${func.substring(0, 200)}...`);
        });
    }
    
} catch (error) {
    console.log(`读取chunk-vendors.5b9b0eed.js失败: ${error.message}`);
}

// 3. 比较三个文件的加密实现
console.log('\n3. 三个文件的加密实现对比:');
try {
    const videojs = fs.readFileSync('video.min.7.9.7.js', 'utf8');
    const plugins = fs.readFileSync('videojs-plugins.min.js', 'utf8');
    const vendors = fs.readFileSync('chunk-vendors.5b9b0eed.js', 'utf8');
    
    console.log('文件大小对比:');
    console.log(`  video.min.7.9.7.js: ${videojs.length} 字符`);
    console.log(`  videojs-plugins.min.js: ${plugins.length} 字符`);
    console.log(`  chunk-vendors.5b9b0eed.js: ${vendors.length} 字符`);
    
    // 检查是否有重复的解密实现
    const videojsDecrypt = videojs.match(/function Qu\([^}]+\{[^}]+\}/g);
    const pluginsDecrypt = plugins.match(/function Qu\([^}]+\{[^}]+\}/g);
    const vendorsDecrypt = vendors.match(/function Qu\([^}]+\{[^}]+\}/g);
    
    console.log('\nQu函数分布:');
    console.log(`  video.min.7.9.7.js: ${videojsDecrypt ? videojsDecrypt.length : 0} 个`);
    console.log(`  videojs-plugins.min.js: ${pluginsDecrypt ? pluginsDecrypt.length : 0} 个`);
    console.log(`  chunk-vendors.5b9b0eed.js: ${vendorsDecrypt ? vendorsDecrypt.length : 0} 个`);
    
} catch (error) {
    console.log(`文件对比失败: ${error.message}`);
}

console.log('\n=== 分析完成 ===');
