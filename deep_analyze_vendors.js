const fs = require('fs');

console.log('=== 深度分析 chunk-vendors.5b9b0eed.js ===\n');

const vendorsContent = fs.readFileSync('chunk-vendors.5b9b0eed.js', 'utf8');

// 1. 查找所有可能的加密库
console.log('1. 查找加密库:');
const cryptoLibs = [
    'CryptoJS', 'crypto-js', 'aes-js', 'node-forge', 'sjcl', 'webcrypto',
    'SubtleCrypto', 'window.crypto', 'crypto.subtle'
];

cryptoLibs.forEach(lib => {
    const regex = new RegExp(lib, 'gi');
    const matches = vendorsContent.match(regex);
    if (matches) {
        console.log(`  ${lib}: ${matches.length} 次`);
    }
});

// 2. 查找AES相关的函数和对象
console.log('\n2. 查找AES相关实现:');
const aesRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*[^,;]*(?:aes|AES|encrypt|decrypt|cipher)[^,;]*/gi;
const aesImplementations = vendorsContent.match(aesRegex);
if (aesImplementations) {
    console.log(`找到 ${aesImplementations.length} 个AES相关实现:`);
    // 去重并显示前10个
    const uniqueAes = [...new Set(aesImplementations)];
    uniqueAes.slice(0, 10).forEach((impl, index) => {
        console.log(`  ${index + 1}. ${impl.substring(0, 80)}...`);
    });
}

// 3. 查找可能的M.prototype.encrypt/decrypt
console.log('\n3. 查找M.prototype相关方法:');
const mPrototypeRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\.prototype\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*function[^}]*(?:encrypt|decrypt|cipher|aes|key|iv)[^}]*\}/gi;
const mPrototypeMethods = vendorsContent.match(mPrototypeRegex);
if (mPrototypeMethods) {
    console.log(`找到 ${mPrototypeMethods.length} 个可能的prototype方法:`);
    mPrototypeMethods.slice(0, 5).forEach((method, index) => {
        console.log(`\n  方法 ${index + 1}:`);
        console.log(`  ${method.substring(0, 150)}...`);
    });
}

// 4. 查找包含encrypt和decrypt的完整对象
console.log('\n4. 查找包含encrypt/decrypt的对象:');
const encryptDecryptObjectRegex = /\{[^}]*encrypt\s*:[^}]*decrypt\s*:[^}]*\}|\{[^}]*decrypt\s*:[^}]*encrypt\s*:[^}]*\}/gi;
const encryptDecryptObjects = vendorsContent.match(encryptDecryptObjectRegex);
if (encryptDecryptObjects) {
    console.log(`找到 ${encryptDecryptObjects.length} 个包含encrypt/decrypt的对象:`);
    encryptDecryptObjects.slice(0, 3).forEach((obj, index) => {
        console.log(`\n  对象 ${index + 1}:`);
        console.log(`  ${obj.substring(0, 200)}...`);
    });
}

// 5. 查找IV处理相关代码
console.log('\n5. 查找IV处理代码:');
const ivRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*[^,;]*iv[^,;]*/gi;
const ivImplementations = vendorsContent.match(ivRegex);
if (ivImplementations) {
    console.log(`找到 ${ivImplementations.length} 个IV相关实现:`);
    // 去重并显示前10个
    const uniqueIv = [...new Set(ivImplementations)];
    uniqueIv.slice(0, 10).forEach((impl, index) => {
        console.log(`  ${index + 1}. ${impl.substring(0, 80)}...`);
    });
}

// 6. 查找可能的CBC模式实现
console.log('\n6. 查找CBC模式实现:');
const cbcRegex = /[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*[^,;]*cbc[^,;]*/gi;
const cbcImplementations = vendorsContent.match(cbcRegex);
if (cbcImplementations) {
    console.log(`找到 ${cbcImplementations.length} 个CBC相关实现:`);
    cbcImplementations.slice(0, 5).forEach((impl, index) => {
        console.log(`  ${index + 1}. ${impl.substring(0, 80)}...`);
    });
}

// 7. 查找可能的密钥处理函数
console.log('\n7. 查找密钥处理函数:');
const keyFunctionRegex = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*key[^}]{20,}\}/gi;
const keyFunctions = vendorsContent.match(keyFunctionRegex);
if (keyFunctions) {
    console.log(`找到 ${keyFunctions.length} 个密钥处理函数:`);
    keyFunctions.slice(0, 3).forEach((func, index) => {
        console.log(`\n  函数 ${index + 1}:`);
        console.log(`  ${func.substring(0, 200)}...`);
    });
}

// 8. 提取可能的完整加密模块
console.log('\n8. 提取可能的加密模块:');
const moduleRegex = /\{[^}]*encrypt\s*:[^}]*decrypt\s*:[^}]*\}|\{[^}]*decrypt\s*:[^}]*encrypt\s*:[^}]*\}/gi;
const modules = vendorsContent.match(moduleRegex);
if (modules) {
    console.log(`找到 ${modules.length} 个可能的加密模块`);
    
    // 保存最有希望的模块
    modules.slice(0, 3).forEach((module, index) => {
        fs.writeFileSync(`crypto_module_${index + 1}.js`, module);
        console.log(`  模块 ${index + 1} 已保存到 crypto_module_${index + 1}.js`);
    });
}

console.log('\n=== 深度分析完成 ===');
