decryptChunk_=function(t,i,n,r){return function(){var e=function(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new g(Array.prototype.slice.call(t)),s=new Uint8Array(e.byteLength),a=new Int32Array(s.buffer),o=void 0,u=void 0,l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],l=i[2],c=i[3],m=0;m<n.length;m+=4)h=v(n[m]),d=v(n[m+1]),p=v(n[m+2]),f=v(n[m+3]),r.decrypt(h,d,p,f,a,m),a[m]=v(a[m]^o),a[m+1]=v(a[m+1]^u),a[m+2]=v(a[m+2]^l),a[m+3]=v(a[m+3]^c),o=h,u=d,l=p,c=f;return s}(t,i,n);r.set(e,t.byteOffset)}}