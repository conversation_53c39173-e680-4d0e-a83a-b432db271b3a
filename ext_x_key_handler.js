);else{if(u=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return l={type:"tag",tagType:"program-date-time"},u[1]&&(l.dateTimeString=u[1],l.dateTimeObject=new Date(u[1])),void c.trigger("data",l);if(u=/^#EXT-X-KEY:?(.*)$/.exec(e))return l={type:"tag",tagType:"key"},u[1]&&(l.attributes=gs(u[1]),l.attributes.IV&&("0x"===l.attributes.IV.substring(0,2).toLowerCase()&&(l.attributes.IV=l.attributes.IV.substring(2)),l.attributes.IV=l.attributes.IV.match(/.{8}/g),l.attributes.IV[0]=parseInt(l.attributes.IV[0],16),l.attributes.IV[1]=parseInt(l.attributes.IV[1],16),l.attributes.IV[2]=parseInt(l.attributes.IV[2],16),l.attributes.IV[3]=parseInt(l.attributes.IV[3],16),l.attributes.IV=new Uint32Array(l.attrib