# Video.js 完整M3U8解密分析报告

## 重要发现

经过深度分析 `video.min.7.9.7.js` 源代码，我确认这**确实包含完整的M3U8解密实现**，包括：

1. **完整的AES-128-CBC解密算法**
2. **EXT-X-KEY标签解析**
3. **IV处理和CBC模式实现**
4. **分段解密处理**

## 核心解密函数分析

### 1. 主解密函数 `Qu(e,t,i)`

```javascript
function Qu(e,t,i){
    var n,r,s,a,o,u,l,c,h,
    d=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),  // 输入数据
    p=new il(Array.prototype.slice.call(t)),                  // AES解密器实例
    f=new Uint8Array(e.byteLength),                          // 输出缓冲区
    m=new Int32Array(f.buffer);                              // 输出32位视图
    
    // 初始化IV
    for(n=i[0],r=i[1],s=i[2],a=i[3],h=0;h<d.length;h+=4)
        o=$u(d[h]),u=$u(d[h+1]),l=$u(d[h+2]),c=$u(d[h+3]),  // 读取4个32位字
        p.decrypt(o,u,l,c,m,h),                              // AES解密
        m[h]=$u(m[h]^n),m[h+1]=$u(m[h+1]^r),               // CBC模式XOR
        m[h+2]=$u(m[h+2]^s),m[h+3]=$u(m[h+3]^a),
        n=o,r=u,s=l,a=c;                                     // 更新IV为前一个密文块
    
    return f
}
```

**参数说明：**
- `e`: 加密的数据缓冲区
- `t`: AES密钥 (16字节)
- `i`: 初始化向量IV (4个32位字)

**关键特点：**
- 实现标准的AES-128-CBC解密
- 使用`$u()`函数处理字节序转换
- 每次处理128位(16字节)数据块
- 正确实现CBC模式的IV链接

### 2. 解密块处理函数 `decryptChunk_`

```javascript
decryptChunk_=function(t,i,n,r){
    return function(){
        var e=Qu(t,i,n);  // 调用主解密函数
        r.set(e,t.byteOffset)  // 将解密结果写入输出缓冲区
    }
}
```

这是一个工厂函数，返回一个可以异步执行的解密任务。

### 3. EXT-X-KEY标签解析

```javascript
if(u=/^#EXT-X-KEY:?(.*)$/.exec(e))
    return l={type:"tag",tagType:"key"},
    u[1]&&(
        l.attributes=gs(u[1]),  // 解析属性
        l.attributes.IV&&(
            "0x"===l.attributes.IV.substring(0,2).toLowerCase()&&
            (l.attributes.IV=l.attributes.IV.substring(2)),  // 移除0x前缀
            l.attributes.IV=l.attributes.IV.match(/.{8}/g),  // 分割为8字符组
            l.attributes.IV[0]=parseInt(l.attributes.IV[0],16),  // 转换为整数
            l.attributes.IV[1]=parseInt(l.attributes.IV[1],16),
            l.attributes.IV[2]=parseInt(l.attributes.IV[2],16),
            l.attributes.IV[3]=parseInt(l.attributes.IV[3],16),
            l.attributes.IV=new Uint32Array(l.attributes.IV)  // 创建32位数组
        )
    )
```

**功能：**
- 解析M3U8文件中的`#EXT-X-KEY`标签
- 提取加密方法、密钥URI、IV等信息
- 将十六进制IV转换为32位整数数组

### 4. AES-128方法处理

```javascript
t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),
s={
    method:t.attributes.METHOD||"AES-128",  // 默认使用AES-128
    uri:t.attributes.URI                    // 密钥文件URI
},
"undefined"!=typeof t.attributes.IV&&(s.iv=t.attributes.IV)  // 设置IV
```

**特点：**
- 默认使用AES-128加密方法
- 支持从URI获取密钥
- 正确处理IV参数

## 完整的M3U8解密流程

### 1. M3U8解析阶段
```
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-KEY:METHOD=AES-128,URI="key.key",IV=0x12345678901234567890123456789012
#EXTINF:10.0,
segment1.ts
#EXTINF:10.0,
segment2.ts
```

### 2. 密钥获取
- 从`EXT-X-KEY`的URI下载密钥文件
- 密钥通常是16字节的二进制数据

### 3. IV处理
- 从`EXT-X-KEY`的IV参数获取初始化向量
- 如果没有IV，通常使用段序号作为IV

### 4. 分段解密
```javascript
// 对每个.ts段进行解密
for (let segment of segments) {
    let encryptedData = downloadSegment(segment.uri);
    let decryptedData = Qu(encryptedData, key, iv);
    // 处理解密后的数据
}
```

## 字节序处理函数 `$u()`

```javascript
function $u(e) {
    // 大端序到小端序转换 (或相反)
    return (e << 24) | ((e & 0xff00) << 8) | ((e & 0xff0000) >> 8) | (e >>> 24);
}
```

这个函数处理不同平台间的字节序差异。

## AES解密器类 `il`

- 实现完整的AES算法
- 支持128/192/256位密钥
- 使用查找表优化性能
- 提供`decrypt()`方法进行单块解密

## 异步处理机制

```javascript
// 异步流处理器
var nl = function(t) {
    // 管理解密任务队列
    // 避免阻塞主线程
    // 使用setTimeout进行任务调度
}
```

## 实际使用示例

```javascript
// 1. 解析M3U8获取加密信息
let keyInfo = parseEXTXKEY(m3u8Content);

// 2. 下载密钥
let key = await downloadKey(keyInfo.uri);

// 3. 准备IV
let iv = keyInfo.iv || generateIVFromSequence(segmentIndex);

// 4. 解密段
let decryptedSegment = Qu(encryptedSegmentData, key, iv);

// 5. 移除PKCS7填充
let finalData = removePKCS7Padding(decryptedSegment);
```

## 总结

**确认：这个Video.js文件包含完整的M3U8解密实现**

包含的核心组件：
1. ✅ 完整的AES-128-CBC解密算法
2. ✅ EXT-X-KEY标签解析
3. ✅ IV处理和CBC模式实现
4. ✅ 分段异步解密处理
5. ✅ 字节序处理
6. ✅ PKCS7填充处理

这个实现完全符合HLS标准，可以直接用于M3U8视频流的解密。
