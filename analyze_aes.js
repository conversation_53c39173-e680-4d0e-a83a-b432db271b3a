const fs = require('fs');

// 读取video.min.7.9.7.js文件
const content = fs.readFileSync('video.min.7.9.7.js', 'utf8');

console.log('=== 文件基本信息 ===');
console.log(`文件大小: ${content.length} 字符`);
console.log(`文件行数: ${content.split('\n').length} 行`);

console.log('\n=== 搜索AES相关内容 ===');

// 搜索AES相关的关键词
const aesKeywords = [
    'aes-decrypter',
    'AES',
    'encrypt',
    'decrypt',
    'M.prototype.encrypt',
    'M.prototype.decrypt',
    'pkcs7',
    'CBC',
    'key',
    'iv',
    'cipher'
];

aesKeywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'gi');
    const matches = content.match(regex);
    if (matches) {
        console.log(`"${keyword}" 出现次数: ${matches.length}`);
    }
});

console.log('\n=== 查找M.prototype相关方法 ===');
// 查找M.prototype的方法定义
const mPrototypeRegex = /M\.prototype\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=/g;
const mPrototypeMethods = content.match(mPrototypeRegex);
if (mPrototypeMethods) {
    console.log('找到的M.prototype方法:');
    mPrototypeMethods.forEach(method => {
        console.log(`  ${method}`);
    });
}

console.log('\n=== 查找加密解密函数 ===');
// 查找可能的加密解密函数
const cryptoRegex = /(encrypt|decrypt|cipher|decipher)\s*[:=]\s*function/gi;
const cryptoFunctions = content.match(cryptoRegex);
if (cryptoFunctions) {
    console.log('找到的加密解密函数:');
    cryptoFunctions.forEach(func => {
        console.log(`  ${func}`);
    });
}

console.log('\n=== 提取aes-decrypter相关代码段 ===');
// 查找aes-decrypter注释附近的代码
const aesDecrypterIndex = content.indexOf('aes-decrypter');
if (aesDecrypterIndex !== -1) {
    // 提取aes-decrypter注释前后的代码
    const start = Math.max(0, aesDecrypterIndex - 500);
    const end = Math.min(content.length, aesDecrypterIndex + 2000);
    const aesSection = content.substring(start, end);
    
    console.log('aes-decrypter 相关代码段:');
    console.log('=' * 50);
    console.log(aesSection);
    console.log('=' * 50);
}

console.log('\n=== 查找可能的AES实现 ===');
// 查找可能包含AES实现的代码块
const aesImplementationRegex = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:aes|AES|encrypt|decrypt|cipher)[^}]*\}/gi;
const aesImplementations = content.match(aesImplementationRegex);
if (aesImplementations) {
    console.log('找到可能的AES实现:');
    aesImplementations.slice(0, 3).forEach((impl, index) => {
        console.log(`\n--- 实现 ${index + 1} ---`);
        console.log(impl.substring(0, 200) + '...');
    });
}

console.log('\n=== 提取完整的AES解密器代码 ===');
// 查找并提取完整的AES解密器实现
const aesDecrypterStart = content.indexOf('/*! @name aes-decrypter');
if (aesDecrypterStart !== -1) {
    // 查找下一个注释或明显的代码块结束
    let aesDecrypterEnd = content.indexOf('/*! @name', aesDecrypterStart + 1);
    if (aesDecrypterEnd === -1) {
        // 如果没找到下一个注释，查找其他可能的结束标志
        aesDecrypterEnd = content.indexOf('var ', aesDecrypterStart + 5000);
        if (aesDecrypterEnd === -1) {
            aesDecrypterEnd = aesDecrypterStart + 10000; // 默认提取10000字符
        }
    }

    const aesDecrypterCode = content.substring(aesDecrypterStart, aesDecrypterEnd);

    // 保存到文件
    fs.writeFileSync('aes_decrypter_extracted.js', aesDecrypterCode);
    console.log('AES解密器代码已保存到 aes_decrypter_extracted.js');
    console.log(`提取的代码长度: ${aesDecrypterCode.length} 字符`);
}

console.log('\n=== 查找M.prototype.encrypt和M.prototype.decrypt ===');
// 更精确地查找M.prototype的encrypt和decrypt方法
const mPrototypeEncryptRegex = /M\.prototype\.encrypt\s*=\s*function[^}]*\{[^}]*\}/g;
const mPrototypeDecryptRegex = /M\.prototype\.decrypt\s*=\s*function[^}]*\{[^}]*\}/g;

const encryptMatch = content.match(mPrototypeEncryptRegex);
const decryptMatch = content.match(mPrototypeDecryptRegex);

if (encryptMatch) {
    console.log('\n找到 M.prototype.encrypt:');
    encryptMatch.forEach((match, index) => {
        console.log(`--- encrypt方法 ${index + 1} ---`);
        console.log(match);
    });
}

if (decryptMatch) {
    console.log('\n找到 M.prototype.decrypt:');
    decryptMatch.forEach((match, index) => {
        console.log(`--- decrypt方法 ${index + 1} ---`);
        console.log(match);
    });
}

console.log('\n=== 查找关键的解密函数 ===');
// 查找可能的解密函数，特别是处理m3u8的
const decryptFunctionRegex = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:decrypt|AES|cipher)[^}]{0,500}\}/gi;
const decryptFunctions = content.match(decryptFunctionRegex);

if (decryptFunctions) {
    console.log('\n找到的解密函数:');
    decryptFunctions.slice(0, 5).forEach((func, index) => {
        console.log(`\n--- 解密函数 ${index + 1} ---`);
        console.log(func);
    });
}

console.log('\n=== 分析完成 ===');
